package commands

import (
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"

	"github.com/frontendninja10/pokedexcli/internal/models"
	"github.com/frontendninja10/pokedexcli/internal/pokecache"
)

type config struct {
	Previous string `json:"previous"`
	Next     string `json:"next"`
	Cache    *pokecache.Cache
}

func catchPokemon(url string, cfg *config) (models.Pokemon, error) {
	body, exists := cfg.Cache.Get(url)
	if exists {
		var pokemon models.Pokemon
		if err := json.Unmarshal(body, &pokemon); err != nil {
			return models.Pokemon{}, fmt.Errorf("error unmarshalling json")
		}
		return pokemon, nil
	}
	res, err := http.Get(url)
	if err != nil {
		return models.Pokemon{}, fmt.Errorf("possible network error")
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return models.Pokemon{}, fmt.Errorf("unsuccessful request: %d", res.StatusCode)
	}

	data, err := io.ReadAll(res.Body)
	if err != nil {
		return models.Pokemon{}, err
	}

	var pokemon models.Pokemon
	if err := json.Unmarshal(data, &pokemon); err != nil {
		return models.Pokemon{}, err
	}

	cfg.Cache.Add(url, data)
	return pokemon, nil
}

func catchCommand(args []string, cfg *config) error {
	if len(args) == 1 {
		return fmt.Errorf("you must provide a Pokemon")
	}

	fmt.Printf("Throwing a Pokeball at %s...", args[1])

	url := "https://pokeapi.co/api/v2/pokemon/pikachu"

	res, err := catchPokemon(url, cfg)
	if err != nil {
		return err
	}
	baseExperience := res.BaseExperience
	randomValue := rand.Intn(baseExperience)

	if baseExperience == randomValue {
		fmt.Printf("%s was caught!", res.Name)
	} else {
		fmt.Printf("%s escaped!", res.Name)
	}
	return nil
}