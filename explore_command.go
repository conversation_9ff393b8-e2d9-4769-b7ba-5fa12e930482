package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type LocationAreaResp struct {
	PokemonEncounters []struct {
		Pokemon struct {
			Name string `json:"name"`
			URL string 	`json:"url"`
		} `json:"pokemon"`
		
		VersionDetails []struct {
			EncounterDetails []struct {
				Chance 	int `json:"chance"`
				ConditionValues []struct {
					Name string `json:"name"`
					URL string 	`json:"url"`
				} `json:"condition_values"`
				MaxLevel int `json:"max_level"`
				Method struct {
					Name string `json:"name"`
					URL string 	`json:"url"`
				}
				MinLevel int `json:"min_level"`
			} `json:"encounter_details"`
			MaxChance int `json:"max_chance"`
			Version struct {
				Name string `json:"name"`
				URL string 	`json:"url"`
			} `json:"version"`
		} `json:"version_details"`
	} `json:"pokemon_encounters"`
}



func exploreLocation(url string, c *config) (LocationAreaResp, error) {
	cache, exists := c.Cache.Get(url)
	if exists {
		var locaionAreaRes LocationAreaResp
		if err := json.Unmarshal(cache, &locaionAreaRes); err != nil {
			return locaionAreaRes, err
		}
		return locaionAreaRes, nil
	}

	res, err := http.Get(url)
	if err != nil {
		return LocationAreaResp{}, fmt.Errorf("error creating request")
	}

	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return LocationAreaResp{}, fmt.Errorf("recieved non-200 status code: %d", res.StatusCode)
	}

	data, err := io.ReadAll(res.Body)
	if err != nil {
		return LocationAreaResp{}, err
	}

	var locaionAreaRes LocationAreaResp

	if err := json.Unmarshal(data, &locaionAreaRes); err != nil {
		return  LocationAreaResp{}, err
	}

	c.Cache.Add(url, data)

	return locaionAreaRes, nil
} 

func exploreCommand(args []string, c *config) error {
	if len(args) == 1 {
		return fmt.Errorf("you must provide a location")
	}
	url := "https://pokeapi.co/api/v2/location-area/" + args[1]

	res, err := exploreLocation(url, c)
	if err != nil {
		return err
	}

	pokemonEncounters := res.PokemonEncounters
	fmt.Printf("Exploring %s...\n", args[1])
	fmt.Println("Found Pokemon:")
	for _, pokemonEncounter := range pokemonEncounters {
		fmt.Printf("- %s\n", pokemonEncounter.Pokemon.Name)
	}

	return nil
}