package main

import (
	"fmt"
)

func mapbCommand(args []string, c *config) error {

	if c.Next == "" && c.Previous == "" {
		return fmt.<PERSON><PERSON><PERSON>("you cannot go back")
	}

	if c.Previous == "" {
		return fmt.<PERSON><PERSON>rf("you are on first page")
	} else {
		url := c.Previous

		res, err := getLocationAreas(url, c)
		if err != nil {
			return err
		}

		c.Next = res.Next
		c.Previous = res.Previous

		for _, area := range res.Results {
			fmt.Println(area.Name)
		}
	}

	return nil
}