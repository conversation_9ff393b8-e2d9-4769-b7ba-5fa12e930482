package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"

	pokecache "github.com/frontendninja10/pokedexcli/internal/pokecache"
)

type cliCommand struct {
	name string
	description string
	callback func(input []string, c *config) error
}

type config struct {
	Previous string `json:"previous"`
	Next string `json:"next"`
	Cache *pokecache.Cache	
}


func startRepl() {
	var c config

	newCacheVal := pokecache.NewCache(60 * time.Second)

	c.Cache = &newCacheVal

	
	reader := bufio.NewScanner(os.Stdin)

	for {
		fmt.Print("pokedex > ")
		reader.Scan()

		input := reader.Text()

		cleanedInput := cleanInput(input)

		if len(cleanedInput) == 0 {
			continue
		}


		command, exists := getCommands()[cleanedInput[0]]
		if exists {
			err := command.callback(cleanedInput, &c)
			if err != nil {
				fmt.Println(err)
			}
			continue
		} else {
			fmt.Println("Unknown command")
		}
		
	}
}

func cleanInput(text string) []string {
	lower := strings.ToLower(text)
	output := strings.Fields(lower)

	return  output
}

func getCommands() map[string]cliCommand {
	return map[string]cliCommand{
		"exit": {
			name:        "exit",
			description: "Exit the Pokedex",
			callback:    exitCommand,
		},
		"help": {
			name:        "help",
			description: "Displays a help message",
			callback:    helpCommand,
		},
		"map": {
			name: "map",
			description: "Displays names of 20 location areas",
			callback: mapCommand,
		},
		"mapb": {
			name: "mapb",
			description: "Displays the previous 20 location areas",
			callback: mapbCommand,
		},
		"explore": {
			name: "explore",
			description: "Displays the names of the Pokemon located in the inputed area",
			callback: exploreCommand,
		},
		"catch": {
			name: "catch",
			description: "Tries to catch a Pokemon",
			callback: catchCommand,
		},
	}
}





