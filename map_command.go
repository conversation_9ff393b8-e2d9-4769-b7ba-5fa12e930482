package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func getLocationAreas(url string, c *config) (ApiResponse, error) {
	cache, exists := c.Cache.Get(url)
	if exists {
		var apiRes ApiResponse
		if err := json.Unmarshal(cache, &apiRes); err != nil {
			return apiRes, err
		}
		return apiRes, nil
	}

	res, err := http.Get(url)
	if err != nil {
		return ApiResponse{}, fmt.Errorf("error creating request: %w", err)
	}

	defer res.Body.Close()

	data, err := io.ReadAll(res.Body)
	if err != nil {
		return ApiResponse{}, err
	}

	var apiRes ApiResponse
	if err := json.Unmarshal(data, &apiRes); err != nil {
		return ApiResponse{}, err
	}

	c.Cache.Add(url, data)

	return  apiRes, nil
}

func mapCommand(args []string, c *config) error {

	var url string

	if c.Next == "" {
		url = "https://pokeapi.co/api/v2/location-area?offset=0&limit=20"
	} else {
		url = c.Next
	}

	res, err := getLocationAreas(url, c)
	if err != nil {
		return err
	}

	c.Previous = res.Previous
	c.Next = res.Next

	for _, area := range res.Results {
		fmt.Println(area.Name)
	}

	return nil

}
